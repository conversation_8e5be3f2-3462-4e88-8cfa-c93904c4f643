{
  "name": "Recruiting: Job + Resume Screening (LLM + PostgreSQL)",
  "nodes": [
    {
      "parameters": {
        "path": "recruiting/job-postings",
        "options": {
          "responseMode": "lastNode",
          "responseData": "allEntries",
          "responseCode": 200
        }
      },
      "id": "Webhook_Job",
      "name": "Webhook: Job posting",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [
        -1400,
        -300
      ],
      "notesInFlow": true,
      "notes": "Accepts POST with manual fields and/or a file upload.\nExpected multipart/form-data or JSON with fields:\n- job_title (string)\n- experience_level (string: Junior|Mid)\n- years_range (string, e.g., \"1-3\")\n- roles (string, multiline)\n- keywords (optional string)\n- behaviours (optional string)\n- uploaded_file (optional: PDF, DOCX, TXT)\n- Optional: job_description_text (string) if no file is provided"
    },
    {
      "parameters": {
        "mode": "waitForWebhookToFinish"
      },
      "id": "Respond_Job",
      "name": "Respond: Job posting",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        800,
        -300
      ],
      "notesInFlow": true,
      "notes": "Returns JSON confirmation with job_id and a workflow link."
    },
    {
      "parameters": {
        "rules": {
          "options": [
            {
              "operation": "boolean",
              "value1": "={{ !!$json[\"binary\"] && !!$binary[\"uploaded_file\"] }}"
            }
          ]
        }
      },
      "id": "IF_Job_HasFile",
      "name": "IF: Job has file?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        -1200,
        -300
      ],
      "notesInFlow": true,
      "notes": "Checks if an uploaded_file binary exists on the webhook input."
    },
    {
      "parameters": {
        "options": {
          "ignoreResponseCode": true,
          "responseFormat": "string",
          "sendBinaryProperty": "uploaded_file",
          "binaryPropertyName": "uploaded_file",
          "contentType": "={{ $binary[\"uploaded_file\"].mimeType || 'application/octet-stream' }}"
        },
        "url": "={{ $json[\"env\"]?.TIKA_URL || 'http://tika:9998/tika' }}",
        "method": "POST"
      },
      "id": "HTTP_Tika_Job",
      "name": "HTTP: Extract text (Tika) [Job]",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4,
      "position": [
        -980,
        -460
      ],
      "credentials": {
        "httpBasicAuth": {
          "id": "TIKA_BASIC_AUTH_CREDENTIAL_ID",
          "name": "Tika Basic Auth (optional)"
        }
      },
      "notesInFlow": true,
      "notes": "Sends the uploaded file to Apache Tika server to extract raw text.\nSet env.TIKA_URL (e.g., http://tika:9998/tika). Supports PDF/DOCX/TXT.\nIf your Tika is open, remove credentials."
    },
    {
      "parameters": {
        "mode": "passThrough",
        "output": "merged"
      },
      "id": "Merge_Job_Text",
      "name": "Merge: Manual + extracted [Job]",
      "type": "n8n-nodes-base.merge",
      "typeVersion": 2,
      "position": [
        -620,
        -460
      ],
      "notesInFlow": true,
      "notes": "Merges manual fields from Webhook with extracted text from Tika."
    },
    {
      "parameters": {
        "functionCode": "const body = $items(\"Webhook: Job posting\", 0)?.json || {};\nconst manual = {\n  job_title: body.job_title || '',\n  experience_level: body.experience_level || '',\n  years_range: body.years_range || '',\n  roles: body.roles || '',\n  keywords: body.keywords || '',\n  behaviours: body.behaviours || ''\n};\n// Extracted text from Tika branch (if present)\nconst tikaText = $items(\"HTTP: Extract text (Tika) [Job]\")?.[0]?.json || '';\n// If no file was provided or Tika not used, try provided job_description_text\nconst fallbackText = body.job_description_text || '';\nconst extracted_text = (typeof tikaText === 'string' ? tikaText : '') || fallbackText || '';\n\n// Build a structured prompt for LLM\nconst prompt = `You are an expert technical recruiter. Given the job details, produce a STRICT JSON object matching this schema (and nothing else):\\n\\n{\n  \"job\": {\n    \"title\": string,\n    \"experience_level\": \"Junior\"|\"Mid\",\n    \"years_range\": string,\n    \"core_responsibilities\": string[],\n    \"must_have_skills\": string[],\n    \"nice_to_have_skills\": string[],\n    \"behaviours\": string[],\n    \"keywords\": string[]\n  },\n  \"workflow\": {\n    \"screening_steps\": [\n      { \"name\": string, \"description\": string }\n    ],\n    \"scorecard\": {\n      \"skills\": string[],\n      \"experience\": string[],\n      \"culture\": string[]\n    }\n  }\n}\\n\\nRules:\\n- Only output valid minified JSON.\\n- Derive lists from roles, keywords, behaviours, and any provided description.\\n- Keep lists concise and role-specific.`;\n\nreturn [{\n  json: {\n    manual,\n    extracted_text,\n    llm_prompt: prompt\n  }\n}];"
      },
      "id": "Fn_Job_Prompt",
      "name": "Function: Build job prompt",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        -420,
        -300
      ],
      "notesInFlow": true,
      "notes": "Normalizes inputs and constructs a strict JSON-only prompt for the LLM."
    },
    {
      "parameters": {
        "resource": "chat",
        "operation": "createChatCompletion",
        "model": "={{ $json.env?.OPENAI_MODEL || 'gpt-4.1-mini' }}",
        "messages": "={{ [{ role: 'system', content: 'You are an expert technical recruiter. Output only valid JSON, no commentary.' }, { role: 'user', content: $json.llm_prompt + '\\n\\nJob details (manual):\\n' + JSON.stringify($json.manual) + '\\n\\nJob description (extracted):\\n' + ($json.extracted_text || '') }] }}",
        "responseFormat": "jsonObject"
      },
      "id": "OpenAI_Job",
      "name": "LLM: Job workflow JSON",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 4,
      "position": [
        -200,
        -300
      ],
      "credentials": {
        "openAiApi": {
          "id": "OPENAI_API_CREDENTIAL_ID",
          "name": "OpenAI API"
        }
      },
      "notesInFlow": true,
      "notes": "Generates structured job workflow JSON using GPT-4.1 mini (configurable)."
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO job_postings (\n  job_title,\n  experience_level,\n  years_range,\n  roles,\n  keywords,\n  behaviours,\n  uploaded_file,\n  workflow_json,\n  created_at\n) VALUES (\n  $1, $2, $3, $4, $5, $6, $7, $8::jsonb, NOW()\n) RETURNING job_id;",
        "additionalFields": {},
        "values": {
          "string": [
            "={{ $items(\"Webhook: Job posting\", 0).json.job_title || ($json.job?.title || '') }}",
            "={{ $items(\"Webhook: Job posting\", 0).json.experience_level || ($json.job?.experience_level || '') }}",
            "={{ $items(\"Webhook: Job posting\", 0).json.years_range || ($json.job?.years_range || '') }}",
            "={{ $items(\"Webhook: Job posting\", 0).json.roles || '' }}",
            "={{ $items(\"Webhook: Job posting\", 0).json.keywords || '' }}",
            "={{ $items(\"Webhook: Job posting\", 0).json.behaviours || '' }}",
            "={{ $items(\"Webhook: Job posting\", 0).binary?.uploaded_file?.fileName || '' }}",
            "={{ JSON.stringify($json) }}"
          ]
        }
      },
      "id": "PG_Insert_Job",
      "name": "PostgreSQL: Insert job",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 1,
      "position": [
        40,
        -300
      ],
      "credentials": {
        "postgres": {
          "id": "POSTGRES_CREDENTIAL_ID",
          "name": "PostgreSQL"
        }
      },
      "notesInFlow": true,
      "notes": "Inserts job and workflow JSON; relies on DB default to generate job_id (UUID/serial)."
    },
    {
      "parameters": {
        "functionCode": "const baseUrl = $items(\"Webhook: Job posting\", 0)?.json?.env?.PUBLIC_BASE_URL || 'https://your-n8n-host';\nconst jobId = $json.rows?.[0]?.job_id || $json[0]?.job_id || $json.job_id || null;\nreturn [{ json: {\n  status: 'ok',\n  message: 'Job posting created',\n  job_id: jobId,\n  workflow_link: `${baseUrl}/jobs/${jobId}`\n}}];"
      },
      "id": "Fn_Job_Response",
      "name": "Function: Build job response",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        280,
        -300
      ],
      "notesInFlow": true,
      "notes": "Formats the HTTP response returned to the recruiter."
    },
    {
      "parameters": {},
      "id": "Sticky_Job_Flow",
      "name": "Sticky: Job flow overview",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        -1480,
        -520
      ],
      "notes": "Job Posting Flow:\n1) Webhook receives job form + optional file.\n2) IF checks for file.\n3) Tika extracts text if file exists.\n4) Function builds LLM prompt.\n5) LLM produces workflow JSON.\n6) PostgreSQL inserts job.\n7) Respond to webhook with job_id."
    },
    {
      "parameters": {
        "path": "recruiting/resumes",
        "options": {
          "responseMode": "lastNode",
          "responseData": "allEntries",
          "responseCode": 200
        }
      },
      "id": "Webhook_Resume",
      "name": "Webhook: Resume upload",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [
        -1400,
        360
      ],
      "notesInFlow": true,
      "notes": "Accepts POST with candidate details and resume.\nExpected multipart/form-data or JSON with fields:\n- job_id (required)\n- candidate_name (string)\n- candidate_email (string)\n- resume_text (optional string if no file)\n- resume_file (optional: PDF/DOCX/TXT)"
    },
    {
      "parameters": {
        "rules": {
          "options": [
            {
              "operation": "boolean",
              "value1": "={{ !!$json[\"binary\"] && !!$binary[\"resume_file\"] }}"
            }
          ]
        }
      },
      "id": "IF_Resume_HasFile",
      "name": "IF: Resume has file?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        -1200,
        360
      ],
      "notesInFlow": true,
      "notes": "Checks if a resume_file binary exists on the webhook input."
    },
    {
      "parameters": {
        "options": {
          "ignoreResponseCode": true,
          "responseFormat": "string",
          "sendBinaryProperty": "resume_file",
          "binaryPropertyName": "resume_file",
          "contentType": "={{ $binary[\"resume_file\"].mimeType || 'application/octet-stream' }}"
        },
        "url": "={{ $json[\"env\"]?.TIKA_URL || 'http://tika:9998/tika' }}",
        "method": "POST"
      },
      "id": "HTTP_Tika_Resume",
      "name": "HTTP: Extract text (Tika) [Resume]",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4,
      "position": [
        -980,
        200
      ],
      "credentials": {
        "httpBasicAuth": {
          "id": "TIKA_BASIC_AUTH_CREDENTIAL_ID",
          "name": "Tika Basic Auth (optional)"
        }
      },
      "notesInFlow": true,
      "notes": "Sends the resume file to Apache Tika server to extract raw text."
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT job_id, job_title, experience_level, years_range, roles, keywords, behaviours, workflow_json\nFROM job_postings\nWHERE job_id = $1\nLIMIT 1;",
        "values": {
          "string": [
            "={{ $items(\"Webhook: Resume upload\", 0).json.job_id }}"
          ]
        }
      },
      "id": "PG_Get_Job",
      "name": "PostgreSQL: Get job",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 1,
      "position": [
        -980,
        520
      ],
      "credentials": {
        "postgres": {
          "id": "POSTGRES_CREDENTIAL_ID",
          "name": "PostgreSQL"
        }
      ],
      "notesInFlow": true,
      "notes": "Fetches the associated job posting and its workflow JSON."
    },
    {
      "parameters": {
        "mode": "passThrough",
        "output": "merged"
      },
      "id": "Merge_Resume_Text",
      "name": "Merge: Resume text + job",
      "type": "n8n-nodes-base.merge",
      "typeVersion": 2,
      "position": [
        -760,
        360
      ],
      "notesInFlow": true,
      "notes": "Merges resume text (from Tika or provided) with the fetched job."
    },
    {
      "parameters": {
        "functionCode": "const body = $items(\"Webhook: Resume upload\", 0)?.json || {};\nconst tikaText = $items(\"HTTP: Extract text (Tika) [Resume]\")?.[0]?.json || '';\nconst resume_text = (typeof tikaText === 'string' ? tikaText : '') || body.resume_text || '';\n\nconst jobRow = $items(\"PostgreSQL: Get job\")?.[0]?.json?.rows?.[0];\nif (!jobRow) {\n  return [{ json: { error: true, message: 'Invalid job_id: job not found' }, pairedItem: { item: 0 } }];\n}\nconst job = jobRow.workflow_json || {};\n\nconst analysisPrompt = `You are an expert technical recruiter. Analyze the candidate for the job using ONLY the schema below.\\nOutput STRICT minified JSON and nothing else.\\n\\nSchema:\\n{\n  \"candidate\": {\"name\": string, \"email\": string},\n  \"job\": {\"title\": string, \"experience_level\": string},\n  \"analysis\": {\n    \"skills_alignment\": [{\"skill\": string, \"evidence\": string, \"match\": 0-1}],\n    \"experience_relevance\": [{\"aspect\": string, \"evidence\": string, \"match\": 0-1}],\n    \"cultural_fit\": [{\"trait\": string, \"evidence\": string, \"match\": 0-1}],\n    \"overall_fit_rating\": 0-100,\n    \"recommendation\": \"Strong Yes\"|\"Yes\"|\"Maybe\"|\"No\",\n    \"summary\": string\n  }\n}\\n\\nRules:\\n- Use the job workflow scorecard as guidance if available.\\n- Match scores must be numeric (0..1).\\n- Keep arrays concise (max 8 items each).`;\n\nreturn [{\n  json: {\n    candidate: {\n      name: body.candidate_name || '',\n      email: body.candidate_email || ''\n    },\n    job_id: body.job_id,\n    resume_text,\n    job_meta: {\n      job_id: jobRow.job_id,\n      job_title: jobRow.job_title,\n      experience_level: jobRow.experience_level,\n      years_range: jobRow.years_range,\n      roles: jobRow.roles,\n      keywords: jobRow.keywords,\n      behaviours: jobRow.behaviours\n    },\n    job_workflow: job,\n    llm_prompt: analysisPrompt\n  }\n}];"
      },
      "id": "Fn_Resume_Prompt",
      "name": "Function: Build resume prompt",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        -540,
        360
      ],
      "notesInFlow": true,
      "notes": "Builds a strict JSON-only prompt for candidate screening using the job context."
    },
    {
      "parameters": {
        "resource": "chat",
        "operation": "createChatCompletion",
        "model": "={{ $json.env?.OPENAI_MODEL || 'gpt-4.1-mini' }}",
        "messages": "={{ [\n  { role: 'system', content: 'You are an expert technical recruiter. Output only valid JSON, no commentary.' },\n  { role: 'user', content: $json.llm_prompt + '\\n\\nJob (meta):\\n' + JSON.stringify($json.job_meta) + '\\n\\nJob (workflow):\\n' + JSON.stringify($json.job_workflow) + '\\n\\nResume:\\n' + ($json.resume_text || '') + '\\n\\nCandidate:\\n' + JSON.stringify($json.candidate) }\n] }}",
        "responseFormat": "jsonObject"
      },
      "id": "OpenAI_Resume",
      "name": "LLM: Resume analysis JSON",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 4,
      "position": [
        -300,
        360
      ],
      "credentials": {
        "openAiApi": {
          "id": "OPENAI_API_CREDENTIAL_ID",
          "name": "OpenAI API"
        }
      },
      "notesInFlow": true,
      "notes": "Analyzes resume against job requirements; returns strict JSON."
    },
    {
      "parameters": {
        "functionCode": "const r = $json;\n// Normalize fields for DB insert\nconst overall = r.analysis?.overall_fit_rating;\nconst recommendation = r.analysis?.recommendation || '';\nreturn [{ json: {\n  job_id: $items(\"Function: Build resume prompt\", 0).json.job_id,\n  candidate_name: $items(\"Function: Build resume prompt\", 0).json.candidate.name,\n  candidate_email: $items(\"Function: Build resume prompt\", 0).json.candidate.email,\n  resume_content: $items(\"Function: Build resume prompt\", 0).json.resume_text || '',\n  screening_results: r,\n  overall_fit_rating: typeof overall === 'number' ? overall : null,\n  recommendation,\n  status: (recommendation === 'Strong Yes' || recommendation === 'Yes') ? 'shortlist' : (recommendation === 'Maybe' ? 'review' : 'reject')\n}}];"
      },
      "id": "Fn_Resume_ForDB",
      "name": "Function: Normalize screening for DB",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        -60,
        360
      ],
      "notesInFlow": true,
      "notes": "Prepares the LLM output and inputs for PostgreSQL insert."
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO candidate_screenings (\n  job_id,\n  candidate_name,\n  candidate_email,\n  resume_content,\n  screening_results,\n  overall_fit_rating,\n  recommendation,\n  status,\n  screened_at\n) VALUES (\n  $1, $2, $3, $4, $5::jsonb, $6, $7, $8, NOW()\n) RETURNING candidate_id;",
        "values": {
          "string": [
            "={{ $json.job_id }}",
            "={{ $json.candidate_name }}",
            "={{ $json.candidate_email }}",
            "={{ $json.resume_content }}",
            "={{ JSON.stringify($json.screening_results) }}",
            "={{ $json.overall_fit_rating }}",
            "={{ $json.recommendation }}",
            "={{ $json.status }}"
          ]
        }
      },
      "id": "PG_Insert_Resume",
      "name": "PostgreSQL: Insert screening",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 1,
      "position": [
        200,
        360
      ],
      "credentials": {
        "postgres": {
          "id": "POSTGRES_CREDENTIAL_ID",
          "name": "PostgreSQL"
        }
      },
      "notesInFlow": true,
      "notes": "Inserts candidate screening linked to job_id."
    },
    {
      "parameters": {
        "functionCode": "const baseUrl = $items(\"Webhook: Resume upload\", 0)?.json?.env?.PUBLIC_BASE_URL || 'https://your-n8n-host';\nconst candidateId = $json.rows?.[0]?.candidate_id || $json.candidate_id || null;\nconst jobId = $items(\"Function: Build resume prompt\", 0)?.json?.job_id;\nconst analysis = $items(\"LLM: Resume analysis JSON\", 0)?.json?.analysis || {};\nreturn [{ json: {\n  status: 'ok',\n  message: 'Resume processed',\n  candidate_id: candidateId,\n  job_id: jobId,\n  summary: analysis.summary || '',\n  overall_fit_rating: analysis.overall_fit_rating ?? null,\n  recommendation: analysis.recommendation || '',\n  link: `${baseUrl}/jobs/${jobId}/candidates/${candidateId}`\n}}];"
      },
      "id": "Fn_Resume_Response",
      "name": "Function: Build resume response",
      "type": "n8n-nodes-base.function",
      "typeVersion": 2,
      "position": [
        440,
        360
      ],
      "notesInFlow": true,
      "notes": "Formats the HTTP response returned to the recruiter."
    },
    {
      "parameters": {
        "mode": "waitForWebhookToFinish"
      },
      "id": "Respond_Resume",
      "name": "Respond: Resume upload",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        680,
        360
      ],
      "notesInFlow": true,
      "notes": "Returns JSON confirmation with candidate_id, job_id, rating, recommendation."
    },
    {
      "parameters": {},
      "id": "Sticky_Resume_Flow",
      "name": "Sticky: Resume flow overview",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        -1480,
        160
      ],
      "notes": "Resume Flow:\n1) Webhook receives resume upload with job_id.\n2) IF checks for file and extracts text via Tika if present.\n3) PostgreSQL fetches job.\n4) Function builds LLM prompt with job + resume.\n5) LLM produces structured screening JSON.\n6) PostgreSQL inserts screening.\n7) Respond to webhook with summary."
    },

    {
      "parameters": {},
      "id": "Sticky_DB_Schema",
      "name": "Sticky: DB schema",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        20,
        -520
      ],
      "notes": "PostgreSQL tables expected:\n\njob_postings(\n  job_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  job_title TEXT,\n  experience_level TEXT,\n  years_range TEXT,\n  roles TEXT,\n  keywords TEXT,\n  behaviours TEXT,\n  uploaded_file TEXT,\n  workflow_json JSONB,\n  created_at TIMESTAMPTZ DEFAULT NOW()\n)\n\ncandidate_screenings(\n  candidate_id BIGSERIAL PRIMARY KEY,\n  job_id UUID REFERENCES job_postings(job_id),\n  candidate_name TEXT,\n  candidate_email TEXT,\n  resume_content TEXT,\n  screening_results JSONB,\n  overall_fit_rating INT,\n  recommendation TEXT,\n  status TEXT,\n  screened_at TIMESTAMPTZ DEFAULT NOW()\n)"
    },
    {
      "parameters": {},
      "id": "Sticky_Creds",
      "name": "Sticky: Credentials",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        -1600,
        -40
      ],
      "notes": "Credentials to configure:\n- OpenAI API (used by both LLM nodes). Model overridable via request body env.OPENAI_MODEL.\n- PostgreSQL (used by PG nodes). Ensure DB has required tables and gen_random_uuid().\n- Tika Basic Auth (optional). Set env.TIKA_URL in request or hardcode URL in HTTP nodes.\n\nWebhook URLs:\n- POST /webhook/recruiting/job-postings\n- POST /webhook/recruiting/resumes"
    },
    {
      "parameters": {},
      "id": "Sticky_Conflict",
      "name": "Sticky: Concurrency & conflicts",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        900,
        -520
      ],
      "notes": "Multiple submissions:\n- Webhooks are stateless; each request is independent.\n- DB ensures unique job_id and candidate_id via defaults.\n- Use transactions/constraints in DB for deduplication if needed (e.g., unique (job_id, candidate_email)).\n- LLM outputs constrained to strict JSON to minimize parsing errors."
    }
  ],
  "connections": {
    "Webhook: Job posting": {
      "main": [
        [
          {
            "node": "IF: Job has file?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "IF: Job has file?": {
      "main": [
        [
          {
            "node": "HTTP: Extract text (Tika) [Job]",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Function: Build job prompt",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "HTTP: Extract text (Tika) [Job]": {
      "main": [
        [
          {
            "node": "Merge: Manual + extracted [Job]",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Merge: Manual + extracted [Job]": {
      "main": [
        [
          {
            "node": "Function: Build job prompt",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Function: Build job prompt": {
      "main": [
        [
          {
            "node": "LLM: Job workflow JSON",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "LLM: Job workflow JSON": {
      "main": [
        [
          {
            "node": "PostgreSQL: Insert job",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "PostgreSQL: Insert job": {
      "main": [
        [
          {
            "node": "Function: Build job response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Function: Build job response": {
      "main": [
        [
          {
            "node": "Respond: Job posting",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },

    "Webhook: Resume upload": {
      "main": [
        [
          {
            "node": "IF: Resume has file?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "IF: Resume has file?": {
      "main": [
        [
          {
            "node": "HTTP: Extract text (Tika) [Resume]",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "PostgreSQL: Get job",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "HTTP: Extract text (Tika) [Resume]": {
      "main": [
        [
          {
            "node": "Merge: Resume text + job",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "PostgreSQL: Get job": {
      "main": [
        [
          {
            "node": "Merge: Resume text + job",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Merge: Resume text + job": {
      "main": [
        [
          {
            "node": "Function: Build resume prompt",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Function: Build resume prompt": {
      "main": [
        [
          {
            "node": "LLM: Resume analysis JSON",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "LLM: Resume analysis JSON": {
      "main": [
        [
          {
            "node": "Function: Normalize screening for DB",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Function: Normalize screening for DB": {
      "main": [
        [
          {
            "node": "PostgreSQL: Insert screening",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "PostgreSQL: Insert screening": {
      "main": [
        [
          {
            "node": "Function: Build resume response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Function: Build resume response": {
      "main": [
        [
          {
            "node": "Respond: Resume upload",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "staticData": {},
  "meta": {
    "instanceId": "recruiting-llm-pg",
    "workflowId": "recruiting-llm-pg-001"
  },
  "settings": {
    "saveDataErrorExecution" : "all",
    "saveExecutionProgress": "DEFAULT",
    "saveManualExecutions": true,
    "executionOrder": "v1"
  },
  "tags": [
    {
      "createdAt": "2025-09-04T18:42:00.000Z",
      "updatedAt": "2025-09-04T18:42:00.000Z",
      "name": "recruiting"
    },
    {
      "createdAt": "2025-09-04T18:42:00.000Z",
      "updatedAt": "2025-09-04T18:42:00.000Z",
      "name": "llm"
    },
    {
      "createdAt": "2025-09-04T18:42:00.000Z",
      "updatedAt": "2025-09-04T18:42:00.000Z",
      "name": "postgres"
    }
  ]
}